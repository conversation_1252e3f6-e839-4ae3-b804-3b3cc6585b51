# Viterbi MLSD (Maximum Likelihood Sequence Detection) C Implementation

이 프로젝트는 AIS (Automatic Identification System) 신호 처리를 위한 Viterbi MLSD 알고리즘의 C언어 구현입니다.

## 개요

Viterbi MLSD는 ISI (Inter-Symbol Interference)가 있는 통신 채널에서 최적의 비트 시퀀스를 검출하는 알고리즘입니다. 이 구현은 다음과 같은 특징을 가집니다:

- 4개 상태 (00, 01, 10, 11)를 가진 트렐리스 구조
- 메모리 길이 2 (이전 2비트 고려)
- ISI를 고려한 8가지 예상 신호값 계산
- Add-Compare-Select 연산을 통한 최적 경로 추적

## 파일 구조

```
├── viterbi_mlsd.h          # 헤더 파일 (구조체 및 함수 선언)
├── viterbi_mlsd.c          # 메인 구현 파일
├── viterbi_mlsd_test.c     # 테스트 및 예제 코드
├── Makefile                # 빌드 설정
└── README.md               # 이 파일
```

## 주요 구조체

### ViterbiStates
```c
typedef struct {
    double path_metrics[NUM_STATES];                    // 각 상태의 경로 메트릭
    int sequences[NUM_STATES][MAX_SEQUENCE_LENGTH];     // 각 상태의 비트 시퀀스
    int sequence_lengths[NUM_STATES];                   // 각 상태의 시퀀스 길이
} ViterbiStates;
```

## 주요 함수

### `void init_viterbi_states(ViterbiStates* states)`
- Viterbi 상태를 초기화합니다
- AIS Start bit 패턴 "01"에 맞춰 상태 01만 메트릭 0으로 설정

### `void viterbi_mlsd(ViterbiStates* states, double rx_data, double main_signal_coeff, double isi_signal_coeff)`
- 메인 Viterbi MLSD 알고리즘을 실행합니다
- 수신 데이터와 신호 계수를 입력받아 상태를 업데이트합니다

### `int get_mlsd_decision(ViterbiStates* states)`
- 모든 상태의 첫 번째 비트가 동일한지 확인하여 결정된 비트를 반환합니다

## 빌드 및 실행

### 컴파일
```bash
make
```

또는 직접 컴파일:
```bash
gcc -o viterbi_test viterbi_mlsd.c viterbi_mlsd_test.c -lm
```

### 실행
```bash
make test
```

또는 직접 실행:
```bash
./viterbi_test
```

### 정리
```bash
make clean
```

## 알고리즘 동작 원리

1. **상태 정의**: 4개 상태 (00, 01, 10, 11)는 이전 2비트의 조합을 나타냅니다
2. **신호값 계산**: ISI를 고려한 8가지 예상 신호값을 미리 계산합니다
3. **브랜치 메트릭**: 수신 신호와 예상 신호의 거리 제곱을 계산합니다
4. **경로 메트릭**: 누적 메트릭을 계산하여 최적 경로를 추적합니다
5. **생존 경로 선택**: Add-Compare-Select 연산으로 최소 메트릭 경로를 선택합니다

## 상태 전이

```
상태 00 ← 상태 00 (비트 0) 또는 상태 10 (비트 0)
상태 01 ← 상태 00 (비트 1) 또는 상태 10 (비트 1)
상태 10 ← 상태 01 (비트 0) 또는 상태 11 (비트 0)
상태 11 ← 상태 01 (비트 1) 또는 상태 11 (비트 1)
```

## 사용 예제

```c
#include "viterbi_mlsd.h"

int main() {
    ViterbiStates states;
    init_viterbi_states(&states);
    
    double main_coeff = 1.0;
    double isi_coeff = 0.3;
    double rx_data = 0.5;
    
    viterbi_mlsd(&states, rx_data, main_coeff, isi_coeff);
    
    int decision = get_mlsd_decision(&states);
    if (decision >= 0) {
        printf("Decided bit: %d\n", decision);
    }
    
    return 0;
}
```

## 참고사항

- 이 구현은 MATLAB 코드 `ais_viterbi_mlsd_with_raw_data_250722.m`의 `viterbi_mlsd` 함수를 C언어로 포팅한 것입니다
- AIS 신호 처리에 최적화되어 있습니다
- 메모리 사용량을 고려하여 `MAX_SEQUENCE_LENGTH`를 적절히 설정해야 합니다
