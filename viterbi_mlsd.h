#ifndef VITERBI_MLSD_H
#define VITERBI_MLSD_H

#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <float.h>

// 상수 정의
#define NUM_STATES 4
#define MAX_SEQUENCE_LENGTH 1000
#define INF_VALUE DBL_MAX

// Viterbi 상태 구조체
typedef struct {
    double path_metrics[NUM_STATES];                    // 각 상태의 경로 메트릭
    int sequences[NUM_STATES][MAX_SEQUENCE_LENGTH];     // 각 상태의 비트 시퀀스
    int sequence_lengths[NUM_STATES];                   // 각 상태의 시퀀스 길이
} ViterbiStates;

// 함수 선언
void init_viterbi_states(ViterbiStates* states);
void viterbi_mlsd(ViterbiStates* states, double rx_data, double main_signal_coeff, double isi_signal_coeff);
void add_bit_to_sequence(int* sequence, int* length, int bit);
void copy_sequence_and_add_bit(const int* src_sequence, int src_length, 
                               int* dst_sequence, int* dst_length, int bit);
void print_viterbi_states(const ViterbiStates* states);

#endif // VITERBI_MLSD_H
