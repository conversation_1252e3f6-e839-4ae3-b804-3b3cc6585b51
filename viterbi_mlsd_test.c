#include "viterbi_mlsd.h"

/**
 * MLSD 비트 결정 함수
 * 모든 상태의 첫 번째 비트가 동일한지 확인하고 결정된 비트를 반환
 */
int get_mlsd_decision(ViterbiStates* states) {
    // 모든 상태의 시퀀스가 비어있지 않은지 확인
    for (int i = 0; i < NUM_STATES; i++) {
        if (states->sequence_lengths[i] == 0) {
            return -1; // 결정할 수 없음
        }
    }
    
    // 모든 상태의 첫 번째 비트가 동일한지 확인
    int first_bit = states->sequences[0][0];
    for (int i = 1; i < NUM_STATES; i++) {
        if (states->sequences[i][0] != first_bit) {
            return -1; // 결정할 수 없음
        }
    }
    
    return first_bit;
}

/**
 * 모든 상태에서 첫 번째 비트 제거
 */
void remove_first_bit_from_all_states(ViterbiStates* states) {
    for (int i = 0; i < NUM_STATES; i++) {
        if (states->sequence_lengths[i] > 0) {
            // 첫 번째 비트 제거 (배열을 앞으로 이동)
            for (int j = 0; j < states->sequence_lengths[i] - 1; j++) {
                states->sequences[i][j] = states->sequences[i][j + 1];
            }
            states->sequence_lengths[i]--;
        }
    }
}

/**
 * 테스트 함수
 */
void test_viterbi_mlsd() {
    printf("=== Viterbi MLSD Test ===\n\n");
    
    ViterbiStates states;
    init_viterbi_states(&states);
    
    // 테스트 파라미터
    double main_signal_coeff = 1.0;
    double isi_signal_coeff = 0.3;
    
    // 테스트 데이터 (예시)
    double test_data[] = {0.5, -0.8, 1.2, -1.1, 0.9, -0.7, 1.3, -0.9};
    int test_data_length = sizeof(test_data) / sizeof(test_data[0]);
    
    printf("Initial state:\n");
    print_viterbi_states(&states);
    
    printf("Processing test data...\n");
    for (int i = 0; i < test_data_length; i++) {
        printf("\n--- Processing sample %d: %.2f ---\n", i, test_data[i]);
        
        // Viterbi MLSD 실행
        viterbi_mlsd(&states, test_data[i], main_signal_coeff, isi_signal_coeff);
        
        printf("After processing:\n");
        print_viterbi_states(&states);
        
        // MLSD 비트 결정 시도
        int decided_bit = get_mlsd_decision(&states);
        if (decided_bit >= 0) {
            printf("MLSD Decision: %d\n", decided_bit);
            
            // 결정된 비트 제거
            remove_first_bit_from_all_states(&states);
            printf("After removing decided bit:\n");
            print_viterbi_states(&states);
        } else {
            printf("MLSD Decision: Not available (sequences differ)\n");
        }
    }
    
    printf("\n=== Test Complete ===\n");
}

/**
 * 메인 함수
 */
int main() {
    test_viterbi_mlsd();
    return 0;
}

/**
 * 컴파일 명령어:
 * gcc -o viterbi_test viterbi_mlsd.c viterbi_mlsd_test.c -lm
 * 
 * 실행:
 * ./viterbi_test
 */
