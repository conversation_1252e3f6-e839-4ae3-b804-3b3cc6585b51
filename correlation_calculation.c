#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// Constants from MATLAB code
#define LEN_PREAMBLE 32
#define OSR 5
#define LEN_PREAMBLE_OS (LEN_PREAMBLE * OSR)  // 160

/**
 * Calculate the L2 norm (Euclidean norm) of a vector
 * @param data: input vector
 * @param length: length of the vector
 * @return: L2 norm of the vector
 */
double calculate_norm(const double* data, int length) {
    double sum_squares = 0.0;
    for (int i = 0; i < length; i++) {
        sum_squares += data[i] * data[i];
    }
    return sqrt(sum_squares);
}

/**
 * Normalize a vector in-place by dividing by its L2 norm
 * @param data: input/output vector to be normalized
 * @param length: length of the vector
 */
void normalize_vector(double* data, int length) {
    double norm = calculate_norm(data, length);
    if (norm > 1e-10) {  // Avoid division by zero
        for (int i = 0; i < length; i++) {
            data[i] /= norm;
        }
    }
}

/**
 * Calculate dot product of two vectors
 * @param vec1: first vector
 * @param vec2: second vector
 * @param length: length of both vectors
 * @return: dot product result
 */
double dot_product(const double* vec1, const double* vec2, int length) {
    double result = 0.0;
    for (int i = 0; i < length; i++) {
        result += vec1[i] * vec2[i];
    }
    return result;
}

/**
 * Calculate correlation value as implemented in MATLAB
 * Equivalent to: correlation_value(symbol_index) = abs((rawdata_excluded_dc)' * preamble_os / norm(preamble_os));
 * 
 * @param rawdata_filtered: filtered raw data array
 * @param dc_level: DC level array
 * @param preamble_os: oversampled preamble pattern (normalized)
 * @param symbol_index: current symbol index (1-based like MATLAB)
 * @param rawdata_len: length of rawdata_filtered array
 * @return: correlation value
 */
double calculate_correlation_value(const double* rawdata_filtered, 
                                 const double* dc_level,
                                 const double* preamble_os,
                                 int symbol_index,
                                 int rawdata_len) {
    
    // Check bounds (convert to 0-based indexing)
    int idx = symbol_index - 1;  // Convert to 0-based
    
    if (symbol_index <= LEN_PREAMBLE_OS || idx >= rawdata_len) {
        return 0.0;
    }
    
    // Allocate temporary buffer for rawdata_excluded_dc
    double* rawdata_excluded_dc = (double*)malloc(LEN_PREAMBLE_OS * sizeof(double));
    if (rawdata_excluded_dc == NULL) {
        fprintf(stderr, "Memory allocation failed\n");
        return 0.0;
    }
    
    // Extract and remove DC: rawdata_excluded_dc = rawdata_filtered(symbol_index - LEN_PREAMBLE_OS + 1 : symbol_index) - dc_level(...)
    int start_idx = idx - LEN_PREAMBLE_OS + 1;  // 0-based indexing
    for (int i = 0; i < LEN_PREAMBLE_OS; i++) {
        int data_idx = start_idx + i;
        if (data_idx >= 0 && data_idx < rawdata_len) {
            rawdata_excluded_dc[i] = rawdata_filtered[data_idx] - dc_level[data_idx];
        } else {
            rawdata_excluded_dc[i] = 0.0;  // Handle boundary conditions
        }
    }
    
    // Normalize rawdata_excluded_dc: rawdata_excluded_dc = rawdata_excluded_dc/norm(rawdata_excluded_dc);
    normalize_vector(rawdata_excluded_dc, LEN_PREAMBLE_OS);
    
    // Calculate correlation: (rawdata_excluded_dc)' * preamble_os / norm(preamble_os)
    double correlation = dot_product(rawdata_excluded_dc, preamble_os, LEN_PREAMBLE_OS);
    
    // Calculate norm of preamble_os
    double preamble_norm = calculate_norm(preamble_os, LEN_PREAMBLE_OS);
    
    // Normalize by preamble norm
    if (preamble_norm > 1e-10) {
        correlation /= preamble_norm;
    }
    
    // Take absolute value
    double result = fabs(correlation);
    
    // Clean up
    free(rawdata_excluded_dc);
    
    return result;
}

/**
 * Optimized version that pre-normalizes preamble_os to avoid repeated norm calculations
 * @param rawdata_filtered: filtered raw data array
 * @param dc_level: DC level array
 * @param preamble_os_normalized: pre-normalized oversampled preamble pattern
 * @param symbol_index: current symbol index (1-based like MATLAB)
 * @param rawdata_len: length of rawdata_filtered array
 * @return: correlation value
 */
double calculate_correlation_value_optimized(const double* rawdata_filtered, 
                                           const double* dc_level,
                                           const double* preamble_os_normalized,
                                           int symbol_index,
                                           int rawdata_len) {
    
    // Check bounds (convert to 0-based indexing)
    int idx = symbol_index - 1;  // Convert to 0-based
    
    if (symbol_index <= LEN_PREAMBLE_OS || idx >= rawdata_len) {
        return 0.0;
    }
    
    // Allocate temporary buffer for rawdata_excluded_dc
    double* rawdata_excluded_dc = (double*)malloc(LEN_PREAMBLE_OS * sizeof(double));
    if (rawdata_excluded_dc == NULL) {
        fprintf(stderr, "Memory allocation failed\n");
        return 0.0;
    }
    
    // Extract and remove DC
    int start_idx = idx - LEN_PREAMBLE_OS + 1;  // 0-based indexing
    for (int i = 0; i < LEN_PREAMBLE_OS; i++) {
        int data_idx = start_idx + i;
        if (data_idx >= 0 && data_idx < rawdata_len) {
            rawdata_excluded_dc[i] = rawdata_filtered[data_idx] - dc_level[data_idx];
        } else {
            rawdata_excluded_dc[i] = 0.0;  // Handle boundary conditions
        }
    }
    
    // Normalize rawdata_excluded_dc
    normalize_vector(rawdata_excluded_dc, LEN_PREAMBLE_OS);
    
    // Calculate correlation with pre-normalized preamble
    double correlation = dot_product(rawdata_excluded_dc, preamble_os_normalized, LEN_PREAMBLE_OS);
    
    // Take absolute value
    double result = fabs(correlation);
    
    // Clean up
    free(rawdata_excluded_dc);
    
    return result;
}

/**
 * Example usage and test function
 */
int main() {
    // Example test data
    double rawdata_filtered[200];
    double dc_level[200];
    double preamble_os[LEN_PREAMBLE_OS];
    
    // Initialize with some test data
    for (int i = 0; i < 200; i++) {
        rawdata_filtered[i] = sin(2.0 * M_PI * i / 20.0) + 0.1 * (rand() / (double)RAND_MAX - 0.5);
        dc_level[i] = 0.05;
    }
    
    // Initialize preamble pattern (example)
    for (int i = 0; i < LEN_PREAMBLE_OS; i++) {
        preamble_os[i] = (i % 10 < 5) ? 1.0 : -1.0;  // Simple pattern
    }
    
    // Test the function
    int test_symbol_index = 170;  // 1-based index
    double correlation = calculate_correlation_value(rawdata_filtered, dc_level, preamble_os, 
                                                   test_symbol_index, 200);
    
    printf("Correlation value at symbol_index %d: %f\n", test_symbol_index, correlation);
    
    return 0;
}
