# Makefile for Viterbi MLSD implementation

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2
LDFLAGS = -lm

# 소스 파일
SOURCES = viterbi_mlsd.c viterbi_mlsd_test.c
OBJECTS = $(SOURCES:.c=.o)
TARGET = viterbi_test

# 기본 타겟
all: $(TARGET)

# 실행 파일 생성
$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET) $(LDFLAGS)

# 오브젝트 파일 생성
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# 정리
clean:
	rm -f $(OBJECTS) $(TARGET)

# 테스트 실행
test: $(TARGET)
	./$(TARGET)

# 디버그 빌드
debug: CFLAGS += -g -DDEBUG
debug: $(TARGET)

# 의존성
viterbi_mlsd.o: viterbi_mlsd.c viterbi_mlsd.h
viterbi_mlsd_test.o: viterbi_mlsd_test.c viterbi_mlsd.h

.PHONY: all clean test debug
