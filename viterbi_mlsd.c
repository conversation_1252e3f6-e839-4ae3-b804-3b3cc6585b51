#include "viterbi_mlsd.h"

/**
 * Viterbi 상태 초기화
 * AIS Start bit 패턴이 "01"로 끝나므로 상태 01(인덱스 1)만 0으로 초기화
 */
void init_viterbi_states(ViterbiStates* states) {
    for (int i = 0; i < NUM_STATES; i++) {
        states->sequence_lengths[i] = 0;
        
        // 경로 메트릭 초기화
        if (i == 1) {  // 상태 01 (인덱스 1)
            states->path_metrics[i] = 0.0;
        } else {
            states->path_metrics[i] = INF_VALUE;
        }
        
        // 시퀀스 초기화
        for (int j = 0; j < MAX_SEQUENCE_LENGTH; j++) {
            states->sequences[i][j] = 0;
        }
    }
}

/**
 * 시퀀스에 비트 추가
 */
void add_bit_to_sequence(int* sequence, int* length, int bit) {
    if (*length < MAX_SEQUENCE_LENGTH) {
        sequence[*length] = bit;
        (*length)++;
    }
}

/**
 * 소스 시퀀스를 복사하고 새 비트 추가
 */
void copy_sequence_and_add_bit(const int* src_sequence, int src_length, 
                               int* dst_sequence, int* dst_length, int bit) {
    *dst_length = 0;
    
    // 기존 시퀀스 복사
    for (int i = 0; i < src_length && i < MAX_SEQUENCE_LENGTH - 1; i++) {
        dst_sequence[i] = src_sequence[i];
        (*dst_length)++;
    }
    
    // 새 비트 추가
    if (*dst_length < MAX_SEQUENCE_LENGTH) {
        dst_sequence[*dst_length] = bit;
        (*dst_length)++;
    }
}

/**
 * Viterbi MLSD 알고리즘 구현
 * ISI를 고려한 Maximum Likelihood Sequence Detection
 */
void viterbi_mlsd(ViterbiStates* states, double rx_data, 
                  double main_signal_coeff, double isi_signal_coeff) {
    
    // ISI를 고려한 8가지 예상 신호 값 계산
    // 형태: [이전비트, 현재비트, 다음비트]의 조합에 따른 신호값
    double h_values[8];
    h_values[0] = -isi_signal_coeff - main_signal_coeff - isi_signal_coeff;  // [-1,-1,-1]
    h_values[1] = -isi_signal_coeff - main_signal_coeff + isi_signal_coeff;  // [-1,-1,+1]
    h_values[2] = -isi_signal_coeff + main_signal_coeff - isi_signal_coeff;  // [-1,+1,-1]
    h_values[3] = -isi_signal_coeff + main_signal_coeff + isi_signal_coeff;  // [-1,+1,+1]
    h_values[4] =  isi_signal_coeff - main_signal_coeff - isi_signal_coeff;  // [+1,-1,-1]
    h_values[5] =  isi_signal_coeff - main_signal_coeff + isi_signal_coeff;  // [+1,-1,+1]
    h_values[6] =  isi_signal_coeff + main_signal_coeff - isi_signal_coeff;  // [+1,+1,-1]
    h_values[7] =  isi_signal_coeff + main_signal_coeff + isi_signal_coeff;  // [+1,+1,+1]
    
    // 새로운 상태 정보를 저장할 임시 변수들
    double new_path_metrics[NUM_STATES];
    int new_sequences[NUM_STATES][MAX_SEQUENCE_LENGTH];
    int new_sequence_lengths[NUM_STATES];
    
    // ========== 상태 00 (이전 2비트: 00) 처리 ==========
    // 상태 00으로 들어올 수 있는 경로:
    // 1) 상태 00에서 비트 0 입력 (00 → 00)
    // 2) 상태 10에서 비트 0 입력 (10 → 00)
    
    double bm_00_0 = pow(rx_data - h_values[0], 2);  // 상태 00→00 (비트패턴: 000)
    double bm_10_0 = pow(rx_data - h_values[4], 2);  // 상태 10→00 (비트패턴: 100)
    
    double pm_cand1 = states->path_metrics[0] + bm_00_0;  // 상태 00에서 오는 경로
    double pm_cand2 = states->path_metrics[2] + bm_10_0;  // 상태 10에서 오는 경로
    
    // 생존 경로 선택 (Add-Compare-Select)
    if (pm_cand1 < pm_cand2) {
        new_path_metrics[0] = pm_cand1;
        copy_sequence_and_add_bit(states->sequences[0], states->sequence_lengths[0],
                                  new_sequences[0], &new_sequence_lengths[0], 0);
    } else {
        new_path_metrics[0] = pm_cand2;
        copy_sequence_and_add_bit(states->sequences[2], states->sequence_lengths[2],
                                  new_sequences[0], &new_sequence_lengths[0], 0);
    }
    
    // ========== 상태 01 (이전 2비트: 01) 처리 ==========
    // 상태 01로 들어올 수 있는 경로:
    // 1) 상태 00에서 비트 1 입력 (00 → 01)
    // 2) 상태 10에서 비트 1 입력 (10 → 01)
    
    double bm_00_1 = pow(rx_data - h_values[1], 2);  // 상태 00→01 (비트패턴: 001)
    double bm_10_1 = pow(rx_data - h_values[5], 2);  // 상태 10→01 (비트패턴: 101)
    
    pm_cand1 = states->path_metrics[0] + bm_00_1;  // 상태 00에서 오는 경로
    pm_cand2 = states->path_metrics[2] + bm_10_1;  // 상태 10에서 오는 경로
    
    if (pm_cand1 < pm_cand2) {
        new_path_metrics[1] = pm_cand1;
        copy_sequence_and_add_bit(states->sequences[0], states->sequence_lengths[0],
                                  new_sequences[1], &new_sequence_lengths[1], 1);
    } else {
        new_path_metrics[1] = pm_cand2;
        copy_sequence_and_add_bit(states->sequences[2], states->sequence_lengths[2],
                                  new_sequences[1], &new_sequence_lengths[1], 1);
    }
    
    // ========== 상태 10 (이전 2비트: 10) 처리 ==========
    // 상태 10으로 들어올 수 있는 경로:
    // 1) 상태 01에서 비트 0 입력 (01 → 10)
    // 2) 상태 11에서 비트 0 입력 (11 → 10)
    
    double bm_01_0 = pow(rx_data - h_values[2], 2);  // 상태 01→10 (비트패턴: 010)
    double bm_11_0 = pow(rx_data - h_values[6], 2);  // 상태 11→10 (비트패턴: 110)
    
    pm_cand1 = states->path_metrics[1] + bm_01_0;  // 상태 01에서 오는 경로
    pm_cand2 = states->path_metrics[3] + bm_11_0;  // 상태 11에서 오는 경로
    
    if (pm_cand1 < pm_cand2) {
        new_path_metrics[2] = pm_cand1;
        copy_sequence_and_add_bit(states->sequences[1], states->sequence_lengths[1],
                                  new_sequences[2], &new_sequence_lengths[2], 0);
    } else {
        new_path_metrics[2] = pm_cand2;
        copy_sequence_and_add_bit(states->sequences[3], states->sequence_lengths[3],
                                  new_sequences[2], &new_sequence_lengths[2], 0);
    }
    
    // ========== 상태 11 (이전 2비트: 11) 처리 ==========
    // 상태 11로 들어올 수 있는 경로:
    // 1) 상태 01에서 비트 1 입력 (01 → 11)
    // 2) 상태 11에서 비트 1 입력 (11 → 11)
    
    double bm_01_1 = pow(rx_data - h_values[3], 2);  // 상태 01→11 (비트패턴: 011)
    double bm_11_1 = pow(rx_data - h_values[7], 2);  // 상태 11→11 (비트패턴: 111)
    
    pm_cand1 = states->path_metrics[1] + bm_01_1;  // 상태 01에서 오는 경로
    pm_cand2 = states->path_metrics[3] + bm_11_1;  // 상태 11에서 오는 경로
    
    if (pm_cand1 < pm_cand2) {
        new_path_metrics[3] = pm_cand1;
        copy_sequence_and_add_bit(states->sequences[1], states->sequence_lengths[1],
                                  new_sequences[3], &new_sequence_lengths[3], 1);
    } else {
        new_path_metrics[3] = pm_cand2;
        copy_sequence_and_add_bit(states->sequences[3], states->sequence_lengths[3],
                                  new_sequences[3], &new_sequence_lengths[3], 1);
    }
    
    // ========== 다음 시간 단계를 위한 상태 업데이트 ==========
    for (int i = 0; i < NUM_STATES; i++) {
        states->path_metrics[i] = new_path_metrics[i];
        states->sequence_lengths[i] = new_sequence_lengths[i];
        
        for (int j = 0; j < new_sequence_lengths[i]; j++) {
            states->sequences[i][j] = new_sequences[i][j];
        }
    }
}

/**
 * Viterbi 상태 정보 출력 (디버깅용)
 */
void print_viterbi_states(const ViterbiStates* states) {
    printf("Viterbi States:\n");
    for (int i = 0; i < NUM_STATES; i++) {
        printf("State %d: metric=%.6f, length=%d, sequence=[", 
               i, states->path_metrics[i], states->sequence_lengths[i]);
        
        for (int j = 0; j < states->sequence_lengths[i] && j < 10; j++) {
            printf("%d", states->sequences[i][j]);
            if (j < states->sequence_lengths[i] - 1 && j < 9) printf(",");
        }
        
        if (states->sequence_lengths[i] > 10) {
            printf("...");
        }
        printf("]\n");
    }
    printf("\n");
}
